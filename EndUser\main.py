import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox
import os
import uuid
import platform
import webbrowser
import hashlib

# Set appearance mode and color theme
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("green")

def get_hardware_id():
    """Generate unique hardware ID"""
    try:
        # Get system info
        system_info = platform.system() + platform.version() + platform.machine()
        system_info += str(uuid.getnode())  # MAC address

        # Create hash
        hardware_hash = hashlib.sha256(system_info.encode()).hexdigest()
        formatted_id = f"{hardware_hash[:4]}-{hardware_hash[4:8]}-{hardware_hash[8:12]}-{hardware_hash[12:16]}"
        return formatted_id.upper()
    except:
        # Fallback
        node = str(uuid.getnode())
        return f"{node[:4]}-{node[4:8]}-{node[8:12]}-{node[12:16]}".upper()

class ModernSoftwareApp:
    def __init__(self):
        # Create main window
        self.root = ctk.CTk()
        self.root.title("My Software 2 - Professional Edition")
        self.root.geometry("600x700")
        self.root.resizable(False, False)  # غير قابل للتوسيع

        # Center window
        self.center_window()

        # Show login screen
        self.show_login_screen()

    def center_window(self):
        """Center window on screen"""
        self.root.update_idletasks()
        width = 600
        height = 700
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def clear_window(self):
        """Clear all widgets"""
        for widget in self.root.winfo_children():
            widget.destroy()

    def show_login_screen(self):
        """Show modern login screen"""
        self.clear_window()

        # Main frame
        main_frame = ctk.CTkFrame(self.root, corner_radius=20)
        main_frame.pack(fill="both", expand=True, padx=30, pady=30)

        # Title
        title_label = ctk.CTkLabel(
            main_frame,
            text="🔐 Login",
            font=ctk.CTkFont(size=28, weight="bold")
        )
        title_label.pack(pady=(40, 40))

        # Email field
        email_label = ctk.CTkLabel(
            main_frame,
            text="📧 Email:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        email_label.pack(anchor="w", padx=40, pady=(0, 5))

        self.email_entry = ctk.CTkEntry(
            main_frame,
            placeholder_text="Enter your email",
            font=ctk.CTkFont(size=12),
            height=40,
            width=350
        )
        self.email_entry.pack(padx=40, pady=(0, 20))

        # Password field
        password_label = ctk.CTkLabel(
            main_frame,
            text="🔑 Password:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        password_label.pack(anchor="w", padx=40, pady=(0, 5))

        self.password_entry = ctk.CTkEntry(
            main_frame,
            placeholder_text="Enter your password",
            show="*",
            font=ctk.CTkFont(size=12),
            height=40,
            width=350
        )
        self.password_entry.pack(padx=40, pady=(0, 40))

        # Login button
        login_button = ctk.CTkButton(
            main_frame,
            text="🚀 Login",
            command=self.handle_login,
            font=ctk.CTkFont(size=16, weight="bold"),
            height=50,
            width=300,
            corner_radius=25
        )
        login_button.pack(pady=(0, 60))

    def handle_login(self):
        """Handle login button click"""
        email = self.email_entry.get().strip()
        password = self.password_entry.get().strip()

        # Validate input
        if not email or not password:
            messagebox.showerror("خطأ", "يرجى إدخال البريد الإلكتروني وكلمة المرور")
            return

        if "@" not in email:
            messagebox.showerror("خطأ", "يرجى إدخال بريد إلكتروني صحيح")
            return

        # Check for license file
        if os.path.exists("license.key"):
            # License exists - show main app
            self.show_main_application()
        else:
            # No license - show activation screen
            self.show_activation_screen(email, password)

    def show_activation_screen(self, email, password):
        """Show activation data screen"""
        self.clear_window()

        # Main frame
        main_frame = ctk.CTkFrame(self.root, corner_radius=20)
        main_frame.pack(fill="both", expand=True, padx=30, pady=30)

        # Title
        title_label = ctk.CTkLabel(
            main_frame,
            text="📋 بيانات التفعيل",
            font=ctk.CTkFont(size=26, weight="bold"),
            text_color="#00D4AA"
        )
        title_label.pack(pady=(30, 25))

        # Get hardware ID
        hwid = get_hardware_id()

        # Store data for easy access
        self.activation_data = {
            "email": email,
            "password": password,
            "hwid": hwid
        }

        # Data fields frame
        data_frame = ctk.CTkFrame(main_frame, corner_radius=15, fg_color="#2B2B2B")
        data_frame.pack(fill="x", padx=20, pady=(0, 20))

        # Email row
        self.create_enhanced_data_row(data_frame, "📧 Email:", email, "email")

        # Password row
        self.create_enhanced_data_row(data_frame, "🔑 Password:", password, "password")

        # Hardware ID row
        self.create_enhanced_data_row(data_frame, "💻 Hardware ID:", hwid, "hwid")

        # Copy all button
        copy_all_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        copy_all_frame.pack(fill="x", padx=20, pady=(10, 20))

        copy_all_btn = ctk.CTkButton(
            copy_all_frame,
            text="📋 نسخ الكل",
            command=self.copy_all_data,
            font=ctk.CTkFont(size=16, weight="bold"),
            fg_color="#FF6B35",
            hover_color="#E55A2B",
            height=45,
            width=200,
            corner_radius=25
        )
        copy_all_btn.pack(pady=10)

        # Contact section
        contact_frame = ctk.CTkFrame(main_frame, corner_radius=15, fg_color="#1A1A1A")
        contact_frame.pack(fill="x", padx=20, pady=(20, 0))

        contact_title = ctk.CTkLabel(
            contact_frame,
            text="📞 للتواصل مع المطور لتفعيل البرنامج",
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color="#00D4AA"
        )
        contact_title.pack(pady=(25, 10))

        # Contact info text
        contact_info = ctk.CTkLabel(
            contact_frame,
            text="اختر الطريقة المناسبة للتواصل:",
            font=ctk.CTkFont(size=12),
            text_color="gray70"
        )
        contact_info.pack(pady=(0, 20))

        # Contact buttons frame
        buttons_frame = ctk.CTkFrame(contact_frame, fg_color="transparent")
        buttons_frame.pack(pady=(0, 25))

        # WhatsApp button
        whatsapp_btn = ctk.CTkButton(
            buttons_frame,
            text="📱 WhatsApp",
            command=lambda: webbrowser.open("https://wa.me/201200578402"),
            font=ctk.CTkFont(size=13, weight="bold"),
            fg_color="#25D366",
            hover_color="#128C7E",
            width=140,
            height=40,
            corner_radius=20
        )
        whatsapp_btn.pack(side="left", padx=8)

        # Telegram button
        telegram_btn = ctk.CTkButton(
            buttons_frame,
            text="✈️ Telegram",
            command=lambda: webbrowser.open("http://t.me/Mohamed_Abdo26"),
            font=ctk.CTkFont(size=13, weight="bold"),
            fg_color="#0088cc",
            hover_color="#0077b3",
            width=140,
            height=40,
            corner_radius=20
        )
        telegram_btn.pack(side="left", padx=8)

        # Facebook button
        facebook_btn = ctk.CTkButton(
            buttons_frame,
            text="📘 Facebook",
            command=lambda: webbrowser.open("https://www.facebook.com/mohamed.abdalkareem.558739?mibextid=rS40aB7S9Ucbxw6v"),
            font=ctk.CTkFont(size=13, weight="bold"),
            fg_color="#1877f2",
            hover_color="#166fe5",
            width=140,
            height=40,
            corner_radius=20
        )
        facebook_btn.pack(side="left", padx=8)

        # Back button
        back_btn = ctk.CTkButton(
            main_frame,
            text="🔙 العودة لتسجيل الدخول",
            command=self.show_login_screen,
            font=ctk.CTkFont(size=12),
            fg_color="gray50",
            hover_color="gray40",
            width=200,
            height=35
        )
        back_btn.pack(side="bottom", pady=(0, 20))

    def create_enhanced_data_row(self, parent, label_text, value, data_key):
        """Create enhanced data row with copy button and right-click menu"""
        row_frame = ctk.CTkFrame(parent, fg_color="#3B3B3B", corner_radius=10)
        row_frame.pack(fill="x", padx=15, pady=8)

        # Inner frame for better layout
        inner_frame = ctk.CTkFrame(row_frame, fg_color="transparent")
        inner_frame.pack(fill="x", padx=15, pady=12)

        # Label
        label = ctk.CTkLabel(
            inner_frame,
            text=label_text,
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color="#00D4AA",
            width=140
        )
        label.pack(side="left", padx=(0, 15))

        # Entry with right-click menu
        entry = ctk.CTkEntry(
            inner_frame,
            font=ctk.CTkFont(size=12),
            height=35,
            width=280,
            corner_radius=8,
            border_color="#00D4AA",
            border_width=2
        )
        entry.pack(side="left", padx=(0, 10))
        entry.insert(0, value)
        entry.configure(state="readonly")

        # Add right-click context menu
        self.add_context_menu(entry, value)

        # Copy button
        copy_btn = ctk.CTkButton(
            inner_frame,
            text="📋",
            command=lambda v=value: self.copy_single_value(v),
            font=ctk.CTkFont(size=14, weight="bold"),
            width=45,
            height=35,
            corner_radius=8,
            fg_color="#4CAF50",
            hover_color="#45A049"
        )
        copy_btn.pack(side="left")

    def add_context_menu(self, widget, value):
        """Add right-click context menu to widget"""
        def show_context_menu(event):
            context_menu = tk.Menu(self.root, tearoff=0, bg="#2B2B2B", fg="white",
                                 activebackground="#00D4AA", activeforeground="white",
                                 font=("Arial", 10))
            context_menu.add_command(label="نسخ", command=lambda: self.copy_single_value(value))

            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()

        widget.bind("<Button-3>", show_context_menu)  # Right click

    def copy_single_value(self, value):
        """Copy single value to clipboard"""
        self.root.clipboard_clear()
        self.root.clipboard_append(value)

        # Show success message
        success_window = ctk.CTkToplevel(self.root)
        success_window.title("تم النسخ")
        success_window.geometry("250x100")
        success_window.resizable(False, False)
        success_window.transient(self.root)

        # Center the success window
        success_window.update_idletasks()
        x = self.root.winfo_x() + (self.root.winfo_width() // 2) - 125
        y = self.root.winfo_y() + (self.root.winfo_height() // 2) - 50
        success_window.geometry(f"250x100+{x}+{y}")

        success_label = ctk.CTkLabel(
            success_window,
            text="✅ تم نسخ النص بنجاح!",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color="#4CAF50"
        )
        success_label.pack(expand=True)

        # Auto close after 1.5 seconds
        success_window.after(1500, success_window.destroy)

    def copy_all_data(self):
        """Copy all activation data to clipboard"""
        all_data = f"""📧 Email: {self.activation_data['email']}
🔑 Password: {self.activation_data['password']}
💻 Hardware ID: {self.activation_data['hwid']}"""

        self.root.clipboard_clear()
        self.root.clipboard_append(all_data)

        # Show success message
        success_window = ctk.CTkToplevel(self.root)
        success_window.title("تم النسخ")
        success_window.geometry("300x120")
        success_window.resizable(False, False)
        success_window.transient(self.root)

        # Center the success window
        success_window.update_idletasks()
        x = self.root.winfo_x() + (self.root.winfo_width() // 2) - 150
        y = self.root.winfo_y() + (self.root.winfo_height() // 2) - 60
        success_window.geometry(f"300x120+{x}+{y}")

        success_label = ctk.CTkLabel(
            success_window,
            text="✅ تم نسخ جميع البيانات بنجاح!",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color="#4CAF50"
        )
        success_label.pack(expand=True)

        # Auto close after 2 seconds
        success_window.after(2000, success_window.destroy)

    def create_data_row(self, parent, label_text, value, row):
        """Create a data row with copy button"""
        row_frame = ctk.CTkFrame(parent, fg_color="transparent")
        row_frame.pack(fill="x", padx=20, pady=10)

        # Label
        label = ctk.CTkLabel(
            row_frame,
            text=label_text,
            font=ctk.CTkFont(size=12, weight="bold"),
            width=150
        )
        label.pack(side="left", padx=(0, 10))

        # Entry
        entry = ctk.CTkEntry(
            row_frame,
            font=ctk.CTkFont(size=11),
            height=30,
            width=200
        )
        entry.pack(side="left", padx=(0, 10))
        entry.insert(0, value)
        entry.configure(state="readonly")

        # Copy button
        copy_btn = ctk.CTkButton(
            row_frame,
            text="📋",
            command=lambda v=value: self.copy_to_clipboard(v),
            font=ctk.CTkFont(size=12),
            width=40,
            height=30
        )
        copy_btn.pack(side="left")

    def copy_to_clipboard(self, text):
        """Copy text to clipboard"""
        self.root.clipboard_clear()
        self.root.clipboard_append(text)
        # Show brief feedback
        messagebox.showinfo("تم النسخ", "تم نسخ النص إلى الحافظة!")

    def show_main_application(self):
        """Show main application when licensed"""
        self.clear_window()

        # Main frame
        main_frame = ctk.CTkFrame(self.root, corner_radius=20)
        main_frame.pack(fill="both", expand=True, padx=30, pady=30)

        # Success message
        success_label = ctk.CTkLabel(
            main_frame,
            text="✅ تم تفعيل البرنامج بنجاح!",
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color="green"
        )
        success_label.pack(pady=(50, 20))

        # Welcome message
        welcome_label = ctk.CTkLabel(
            main_frame,
            text="🚀 مرحباً بك في My Software 2!",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        welcome_label.pack(pady=(0, 20))

        # Features info
        features_label = ctk.CTkLabel(
            main_frame,
            text="جميع المميزات متاحة الآن\nشكراً لك على استخدام البرنامج",
            font=ctk.CTkFont(size=14),
            text_color="gray70"
        )
        features_label.pack(pady=(0, 40))

        # Sample feature buttons
        features_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        features_frame.pack(pady=20)

        feature1_btn = ctk.CTkButton(
            features_frame,
            text="📊 التحليلات",
            font=ctk.CTkFont(size=12),
            width=120,
            height=40
        )
        feature1_btn.pack(side="left", padx=10)

        feature2_btn = ctk.CTkButton(
            features_frame,
            text="⚙️ الإعدادات",
            font=ctk.CTkFont(size=12),
            width=120,
            height=40
        )
        feature2_btn.pack(side="left", padx=10)

        feature3_btn = ctk.CTkButton(
            features_frame,
            text="📈 التقارير",
            font=ctk.CTkFont(size=12),
            width=120,
            height=40
        )
        feature3_btn.pack(side="left", padx=10)

        # Exit button
        exit_btn = ctk.CTkButton(
            main_frame,
            text="❌ إغلاق البرنامج",
            command=self.root.quit,
            font=ctk.CTkFont(size=12),
            fg_color="red",
            hover_color="darkred",
            width=150,
            height=35
        )
        exit_btn.pack(side="bottom", pady=(0, 30))

    def run(self):
        """Start the application"""
        self.root.mainloop()

if __name__ == "__main__":
    app = ModernSoftwareApp()
    app.run()